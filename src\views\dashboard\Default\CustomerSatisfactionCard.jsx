import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import { Avatar, Box, Card, Chip, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';

// third party
import Chart from 'react-apexcharts';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';

// assets
import SentimentSatisfiedIcon from '@mui/icons-material/SentimentSatisfied';
import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AssessmentIcon from '@mui/icons-material/Assessment';

// API service
const API_BASE_URL = 'https://laravel-api.fly.dev/api';

export default function CustomerSatisfactionCard() {
  const theme = useTheme();

  // State for API data
  const [metrics, setMetrics] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch statistics from API
  const fetchStatistics = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`${API_BASE_URL}/session-summaries/statistics`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.data) {
        const apiData = result.data;
        const transformedMetrics = {
          total_clients: parseInt(apiData.satisfaction_stats?.total_satisfied || 0) + 
                        parseInt(apiData.satisfaction_stats?.total_neutral || 0) + 
                        parseInt(apiData.satisfaction_stats?.total_unsatisfied || 0),
          satisfied_count: parseInt(apiData.satisfaction_stats?.total_satisfied) || 0,
          neutral_count: parseInt(apiData.satisfaction_stats?.total_neutral) || 0,
          unsatisfied_count: parseInt(apiData.satisfaction_stats?.total_unsatisfied) || 0,
          satisfaction_trend: apiData.satisfaction_stats?.total_satisfied > apiData.satisfaction_stats?.total_unsatisfied ? 'positive' : 'negative'
        };
        
        setMetrics(transformedMetrics);
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (err) {
      console.error('Error fetching statistics:', err);
      setError(err.message);
      
      // Fallback to default data if API fails
      setMetrics({
        total_clients: 0,
        satisfied_count: 0,
        neutral_count: 0,
        unsatisfied_count: 0,
        satisfaction_trend: 'neutral'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchStatistics();
  }, []);

  if (!metrics) {
    return <SkeletonTotalGrowthBarChart />;
  }

  if (error) {
    return (
      <MainCard>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '300px',
            backgroundColor: 'error.lighter',
            borderRadius: 2,
            border: 2,
            borderColor: 'error.main',
            p: 3
          }}
        >
          <Typography variant="h6" sx={{ color: 'error.main', mb: 2 }}>
            ❌ Erreur de chargement
          </Typography>
          <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
            Impossible de charger les données de satisfaction client
          </Typography>
        </Box>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  // Calculate percentages
  const totalClients = metrics.total_clients;
  const satisfiedPercentage = totalClients > 0 ? (metrics.satisfied_count / totalClients) * 100 : 0;
  const neutralPercentage = totalClients > 0 ? (metrics.neutral_count / totalClients) * 100 : 0;
  const unsatisfiedPercentage = totalClients > 0 ? (metrics.unsatisfied_count / totalClients) * 100 : 0;

  // Chart data for satisfaction trend
  const chartOptions = {
    chart: {
      type: 'donut',
      height: 200,
      toolbar: { show: false }
    },
    colors: [theme.palette.success.main, theme.palette.warning.main, theme.palette.error.main],
    labels: ['Satisfaits', 'Neutres', 'Insatisfaits'],
    legend: {
      show: false
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return Math.round(val) + '%';
      }
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%'
        }
      }
    }
  };

  const chartSeries = [satisfiedPercentage, neutralPercentage, unsatisfiedPercentage];

  return (
    <MainCard sx={{ p: { xs: 2, sm: 3 } }}>
      <Box sx={{ width: '100%' }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <AssessmentIcon sx={{ color: 'primary.main', mr: 2, fontSize: '2rem' }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                Satisfaction Client
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Vue d'ensemble de la satisfaction client
              </Typography>
            </Box>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Key Metrics */}
          <Grid xs={12} md={8}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              {/* Total Clients */}
              <Box sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 8px)' } }}>
                <Card sx={{ p: 2.5, textAlign: 'center', height: '120px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                    {totalClients.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Clients Analysés
                  </Typography>
                </Card>
              </Box>

              {/* Satisfaction Rate */}
              <Box sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 8px)' } }}>
                <Card sx={{ p: 2.5, textAlign: 'center', height: '120px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main', mb: 1 }}>
                    {satisfiedPercentage.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Taux de Satisfaction
                  </Typography>
                </Card>
              </Box>
            </Box>
          </Grid>

          {/* Chart */}
          <Grid xs={12} md={4}>
            <Card sx={{ p: 2, height: '200px' }}>
              <Typography variant="h6" sx={{ mb: 2, textAlign: 'center' }}>
                Répartition
              </Typography>
              <Chart options={chartOptions} series={chartSeries} type="donut" height={150} />
            </Card>
          </Grid>
        </Grid>

        {/* Detailed Breakdown */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Détail par Sentiment
          </Typography>
          <Grid container spacing={2}>
            {/* Satisfied */}
            <Grid xs={12} sm={4}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'success.light' }}>
                <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                  <SentimentSatisfiedIcon />
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700, color: 'success.main' }}>
                  {metrics.satisfied_count}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Clients Satisfaits ({satisfiedPercentage.toFixed(1)}%)
                </Typography>
              </Card>
            </Grid>

            {/* Neutral */}
            <Grid xs={12} sm={4}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'warning.light' }}>
                <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 1 }}>
                  <SentimentNeutralIcon />
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700, color: 'warning.main' }}>
                  {metrics.neutral_count}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Clients Neutres ({neutralPercentage.toFixed(1)}%)
                </Typography>
              </Card>
            </Grid>

            {/* Unsatisfied */}
            <Grid xs={12} sm={4}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'error.light' }}>
                <Avatar sx={{ bgcolor: 'error.main', mx: 'auto', mb: 1 }}>
                  <SentimentDissatisfiedIcon />
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700, color: 'error.main' }}>
                  {metrics.unsatisfied_count}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Clients Insatisfaits ({unsatisfiedPercentage.toFixed(1)}%)
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Action Insights */}
        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            💡 Recommandations
          </Typography>
          {satisfiedPercentage >= 70 ? (
            <Typography variant="body2" color="success.main">
              ✅ Excellente satisfaction client ! Continuez sur cette voie.
            </Typography>
          ) : satisfiedPercentage >= 50 ? (
            <Typography variant="body2" color="warning.main">
              ⚠️ Satisfaction modérée. Analysez les retours clients neutres pour améliorer.
            </Typography>
          ) : (
            <Typography variant="body2" color="error.main">
              🚨 Satisfaction faible. Action urgente requise pour améliorer l'expérience client.
            </Typography>
          )}
        </Box>
      </Box>
    </MainCard>
  );
}

CustomerSatisfactionCard.propTypes = {
  // No props needed - component manages its own data
};
